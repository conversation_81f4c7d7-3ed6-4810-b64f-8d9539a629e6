# Milestone Billing Module - Installation Guide

## Quick Installation

### Step 1: Upload Files
1. Copy the entire `milestone_billing` folder to your Perfex CRM `modules/` directory
2. Ensure the web server has read/write permissions on the module files

### Step 2: Install Module
1. Log in to your Perfex CRM admin panel
2. Navigate to **Setup → Modules**
3. Find "Milestone Billing" in the available modules list
4. Click the **Install** button
5. After installation completes, click **Activate**

### Step 3: Configure Permissions
1. Go to **Setup → Staff → Roles**
2. Edit each role that should have access to milestone billing
3. Configure the following permissions as needed:
   - **View**: Allow viewing milestone billing information
   - **Create**: Allow creating milestone billing configurations
   - **Edit**: Allow editing milestone billing settings
   - **Delete**: Allow deleting milestone billing records

### Step 4: Verify Installation
1. Navigate to any project in your CRM
2. You should see a new "Milestone Billing" tab
3. When creating/editing milestones, you should see billing fields
4. Check **Projects** menu for "Milestone Billing" submenu item

## Database Changes

The installation automatically creates/modifies these database tables:

### Modified Tables
- `tblmilestones`: Adds billing-related columns
  - `rate` (decimal): Billing rate
  - `billing_type` (tinyint): Type of billing
  - `is_billable` (boolean): Whether milestone is billable
  - `billed` (boolean): Whether milestone has been billed

### New Tables
- `tblmilestone_billing_items`: Tracks billing records
- `tblmilestone_billing_settings`: Project billing settings

## Post-Installation Setup

### 1. Configure Default Settings
1. Go to any project with milestones
2. Navigate to the "Milestone Billing" tab
3. Configure project-specific billing settings if needed

### 2. Set Up Existing Milestones
1. Edit existing milestones to add billing information
2. Check "Make this milestone billable"
3. Select appropriate billing type and rate

### 3. Test Functionality
1. Create a test milestone with billing enabled
2. Complete the milestone tasks
3. Try creating an invoice from the milestone
4. Verify the invoice is created correctly

## Troubleshooting Installation

### Module Not Appearing
- Check file permissions on the modules directory
- Ensure all files were uploaded correctly
- Check PHP error logs for any issues

### Database Errors
- Verify database user has CREATE and ALTER permissions
- Check that table prefix matches your CRM configuration
- Review the installation logs in Setup → Modules

### Permission Issues
- Ensure staff roles have appropriate permissions
- Check that the user has access to the Projects module
- Verify milestone creation permissions are enabled

## Uninstallation

To remove the module:

1. Go to **Setup → Modules**
2. Find "Milestone Billing" and click **Deactivate**
3. Click **Uninstall** to remove the module
4. Optionally, remove the database tables manually if needed

**Note**: Uninstalling will remove all milestone billing data. Export any important data before uninstalling.

## Requirements

- Perfex CRM version 2.3.0 or higher
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Projects module enabled
- Invoices module enabled

## File Structure

After installation, the module structure should be:

```
modules/milestone_billing/
├── controllers/
│   └── Milestone_billing.php
├── models/
│   └── Milestone_billing_model.php
├── views/
│   ├── admin_head_scripts.php
│   ├── invoice_milestone_options.php
│   ├── manage.php
│   ├── milestone_rate_fields.php
│   ├── project_milestone_billing.php
│   ├── project_scripts.php
│   └── tables/
│       └── milestone_billing.php
├── language/
│   └── english/
│       └── milestone_billing_lang.php
├── helpers/
│   └── milestone_billing_helper.php
├── milestone_billing.php
├── install.php
├── index.html
├── README.md
└── INSTALLATION.md
```

## Support

If you encounter issues during installation:

1. Check the Perfex CRM error logs
2. Verify all requirements are met
3. Review the troubleshooting section in README.md
4. Contact your system administrator for assistance
