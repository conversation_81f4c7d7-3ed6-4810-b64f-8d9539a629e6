<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Milestone_billing extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('milestone_billing_model');
        $this->load->model('projects_model');
        $this->load->model('invoices_model');
    }

    /**
     * List all milestone billing records
     */
    public function index()
    {
        if (!staff_can('view', 'milestone_billing')) {
            access_denied('milestone_billing');
        }

        $data['title'] = _l('milestone_billing');
        $this->load->view('milestone_billing/manage', $data);
    }

    /**
     * Get milestone billing data for datatables
     */
    public function table()
    {
        if (!staff_can('view', 'milestone_billing')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(MILESTONE_BILLING_MODULE_NAME, 'tables/milestone_billing'));
    }

    /**
     * Create invoice from milestones
     */
    public function create_milestone_invoice($project_id)
    {
        if (!staff_can('create', 'invoices')) {
            access_denied('invoices');
        }

        $data = $this->input->post();
        
        if (empty($data['milestones'])) {
            set_alert('warning', _l('milestone_billing_no_milestones_selected'));
            redirect(admin_url('projects/view/' . $project_id));
        }

        $invoice_data = [
            'clientid' => $this->projects_model->get($project_id)->clientid,
            'project_id' => $project_id,
            'date' => date('Y-m-d'),
            'duedate' => date('Y-m-d', strtotime('+30 days')),
            'currency' => get_base_currency()->id,
            'newitems' => []
        ];

        $total_amount = 0;
        $milestone_descriptions = [];

        foreach ($data['milestones'] as $milestone_id) {
            $milestone = $this->milestone_billing_model->get_milestone_with_billing($milestone_id);
            
            if (!$milestone || $milestone['billed']) {
                continue;
            }

            $amount = $this->milestone_billing_model->calculate_milestone_amount($milestone, $project_id);
            
            if ($amount > 0) {
                $invoice_data['newitems'][] = [
                    'description' => _l('milestone_billing_invoice_item', $milestone['name']),
                    'long_description' => $milestone['description'],
                    'qty' => 1,
                    'rate' => $amount,
                    'taxname_1' => '',
                    'taxrate_1' => 0,
                    'taxname_2' => '',
                    'taxrate_2' => 0,
                    'unit' => ''
                ];

                $total_amount += $amount;
                $milestone_descriptions[] = $milestone['name'];
            }
        }

        if (empty($invoice_data['newitems'])) {
            set_alert('warning', _l('milestone_billing_no_billable_amount'));
            redirect(admin_url('projects/view/' . $project_id));
        }

        // Create the invoice
        $invoice_id = $this->invoices_model->add($invoice_data);

        if ($invoice_id) {
            // Mark milestones as billed and create billing records
            foreach ($data['milestones'] as $milestone_id) {
                $milestone = $this->milestone_billing_model->get_milestone_with_billing($milestone_id);
                
                if ($milestone && !$milestone['billed']) {
                    $amount = $this->milestone_billing_model->calculate_milestone_amount($milestone, $project_id);
                    
                    if ($amount > 0) {
                        // Mark milestone as billed
                        $this->milestone_billing_model->mark_milestone_billed($milestone_id);
                        
                        // Create billing record
                        $this->milestone_billing_model->create_billing_record([
                            'milestone_id' => $milestone_id,
                            'invoice_id' => $invoice_id,
                            'project_id' => $project_id,
                            'amount' => $amount,
                            'billing_type' => $milestone['billing_type'],
                            'rate_used' => $milestone['rate'],
                            'description' => _l('milestone_billing_invoice_item', $milestone['name']),
                            'created_by' => get_staff_user_id()
                        ]);
                    }
                }
            }

            // Log activity
            $this->projects_model->log_activity($project_id, 'project_activity_milestone_invoiced', 
                format_invoice_number($invoice_id) . ' (' . implode(', ', $milestone_descriptions) . ')');

            set_alert('success', _l('milestone_billing_invoice_created_successfully'));
            redirect(admin_url('invoices/list_invoices/' . $invoice_id));
        } else {
            set_alert('danger', _l('milestone_billing_invoice_creation_failed'));
            redirect(admin_url('projects/view/' . $project_id));
        }
    }

    /**
     * Update milestone billing settings
     */
    public function update_milestone_billing($milestone_id)
    {
        if (!staff_can('edit', 'milestone_billing')) {
            ajax_access_denied();
        }

        $data = [
            'rate' => $this->input->post('rate'),
            'billing_type' => $this->input->post('billing_type'),
            'is_billable' => $this->input->post('is_billable') ? 1 : 0
        ];

        $success = $this->milestone_billing_model->update_milestone_billing_data($milestone_id, $data);

        if ($success) {
            set_alert('success', _l('milestone_billing_updated_successfully'));
        } else {
            set_alert('danger', _l('milestone_billing_update_failed'));
        }

        echo json_encode(['success' => $success]);
    }

    /**
     * Get project milestone billing overview
     */
    public function project_overview($project_id)
    {
        if (!staff_can('view', 'milestone_billing')) {
            access_denied('milestone_billing');
        }

        $data['project'] = $this->projects_model->get($project_id);
        $data['milestones'] = $this->milestone_billing_model->get_project_milestones_with_billing($project_id);
        $data['billing_summary'] = $this->milestone_billing_model->get_project_billing_summary($project_id);
        $data['project_id'] = $project_id;

        $this->load->view('milestone_billing/project_milestone_billing', $data);
    }

    /**
     * Get milestone billing statistics
     */
    public function get_statistics()
    {
        if (!staff_can('view', 'milestone_billing')) {
            ajax_access_denied();
        }

        $stats = $this->milestone_billing_model->get_milestone_billing_statistics();
        echo json_encode($stats);
    }

    /**
     * Get project milestone billing statistics
     */
    public function get_project_statistics($project_id)
    {
        if (!staff_can('view', 'milestone_billing')) {
            ajax_access_denied();
        }

        $stats = $this->milestone_billing_model->get_project_milestone_billing_statistics($project_id);
        echo json_encode($stats);
    }

    /**
     * Mark milestone as completed and ready for billing
     */
    public function mark_milestone_completed($milestone_id)
    {
        if (!staff_can('edit', 'milestone_billing')) {
            ajax_access_denied();
        }

        $success = $this->milestone_billing_model->mark_milestone_completed($milestone_id);
        
        if ($success) {
            set_alert('success', _l('milestone_marked_completed'));
        } else {
            set_alert('danger', _l('milestone_completion_failed'));
        }

        echo json_encode(['success' => $success]);
    }

    /**
     * Unmark milestone billing (remove from invoice)
     */
    public function unmark_milestone_billed($milestone_id)
    {
        if (!staff_can('edit', 'milestone_billing')) {
            ajax_access_denied();
        }

        $success = $this->milestone_billing_model->unmark_milestone_billed($milestone_id);

        if ($success) {
            set_alert('success', _l('milestone_billing_removed'));
        } else {
            set_alert('danger', _l('milestone_billing_removal_failed'));
        }

        echo json_encode(['success' => $success]);
    }

    /**
     * Handle milestone save with billing data
     */
    public function handle_milestone_save()
    {
        if (!$this->input->post()) {
            echo json_encode(['success' => false, 'message' => 'No data received']);
            return;
        }

        $milestone_id = $this->input->post('id');
        $is_billable = $this->input->post('is_billable') ? 1 : 0;
        $billing_type = $this->input->post('milestone_billing_type');
        $rate = $this->input->post('milestone_rate');

        // If this is a new milestone, we'll handle it after the milestone is created
        if (!$milestone_id) {
            // Store billing data in session for after milestone creation
            $this->session->set_userdata('pending_milestone_billing', [
                'is_billable' => $is_billable,
                'billing_type' => $billing_type,
                'rate' => $rate
            ]);
            echo json_encode(['success' => true]);
            return;
        }

        // Update existing milestone
        $data = [
            'is_billable' => $is_billable,
            'billing_type' => $billing_type,
            'rate' => $rate
        ];

        $success = $this->milestone_billing_model->update_milestone_billing_data($milestone_id, $data);

        echo json_encode(['success' => $success]);
    }

    /**
     * Get milestone billing data for editing
     */
    public function get_milestone_billing_data($milestone_id)
    {
        if (!staff_can('view', 'milestone_billing')) {
            ajax_access_denied();
        }

        $milestone = $this->milestone_billing_model->get_milestone_with_billing($milestone_id);

        if ($milestone) {
            echo json_encode([
                'success' => true,
                'is_billable' => $milestone['is_billable'],
                'billing_type' => $milestone['billing_type'],
                'rate' => $milestone['rate']
            ]);
        } else {
            echo json_encode(['success' => false]);
        }
    }

    /**
     * Process pending milestone billing data after milestone creation
     */
    public function process_pending_milestone_billing($milestone_id)
    {
        $pending_data = $this->session->userdata('pending_milestone_billing');

        if ($pending_data && $milestone_id) {
            $this->milestone_billing_model->update_milestone_billing_data($milestone_id, $pending_data);
            $this->session->unset_userdata('pending_milestone_billing');
        }
    }
}
