<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="row">
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fa fa-money-bill"></i> <?= _l('milestone_billing_overview'); ?>
                </h4>
                <div class="panel-actions">
                    <?php if (staff_can('create', 'invoices') && !empty($milestones)) { ?>
                    <button type="button" class="btn btn-success btn-sm" onclick="open_milestone_invoice_modal()">
                        <i class="fa fa-plus"></i> <?= _l('milestone_billing_create_invoice'); ?>
                    </button>
                    <?php } ?>
                </div>
            </div>
            <div class="panel-body">
                
                <!-- Billing Summary Widget -->
                <div class="row">
                    <div class="col-md-12">
                        <?= get_project_milestone_billing_widget($project_id); ?>
                    </div>
                </div>

                <hr>

                <!-- Milestones Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-bordered" id="milestone-billing-table">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <div class="checkbox">
                                        <input type="checkbox" id="select_all_milestones">
                                        <label for="select_all_milestones"></label>
                                    </div>
                                </th>
                                <th width="20%"><?= _l('milestone_billing_milestone_name'); ?></th>
                                <th width="10%"><?= _l('milestone_billing_due_date'); ?></th>
                                <th width="15%"><?= _l('milestone_billing_completion'); ?></th>
                                <th width="10%"><?= _l('milestone_billing_type'); ?></th>
                                <th width="10%"><?= _l('milestone_billing_rate'); ?></th>
                                <th width="10%"><?= _l('milestone_billing_calculated_amount'); ?></th>
                                <th width="10%"><?= _l('milestone_billing_status'); ?></th>
                                <th width="10%"><?= _l('milestone_billing_actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($milestones)) { ?>
                                <?php foreach ($milestones as $milestone) { ?>
                                <tr data-milestone-id="<?= $milestone['id']; ?>">
                                    <td>
                                        <?php if ($milestone['is_billable'] && !$milestone['billed'] && can_milestone_be_billed($milestone)) { ?>
                                        <div class="checkbox">
                                            <input type="checkbox" name="milestone_ids[]" value="<?= $milestone['id']; ?>" id="milestone_<?= $milestone['id']; ?>">
                                            <label for="milestone_<?= $milestone['id']; ?>"></label>
                                        </div>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <strong><?= e($milestone['name']); ?></strong>
                                        <?php if ($milestone['description']) { ?>
                                        <br><small class="text-muted"><?= e(substr($milestone['description'], 0, 100)); ?><?= strlen($milestone['description']) > 100 ? '...' : ''; ?></small>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <?= $milestone['due_date'] ? _d($milestone['due_date']) : '-'; ?>
                                    </td>
                                    <td>
                                        <?= milestone_progress_bar($milestone); ?>
                                        <small class="text-muted">
                                            <?= $milestone['completed_tasks']; ?>/<?= $milestone['total_tasks']; ?> tasks
                                            <?php if ($milestone['total_logged_hours'] > 0) { ?>
                                            <br><?= $milestone['total_logged_hours']; ?>h logged
                                            <?php } ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($milestone['is_billable']) { ?>
                                            <?= get_milestone_billing_type_icon($milestone['billing_type']); ?>
                                            <?= get_milestone_billing_type($milestone['billing_type'])['name'] ?? '-'; ?>
                                        <?php } else { ?>
                                            <span class="text-muted">-</span>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <?= get_milestone_rate_display($milestone); ?>
                                    </td>
                                    <td>
                                        <?php if ($milestone['is_billable'] && $milestone['calculated_amount'] > 0) { ?>
                                            <strong class="text-success"><?= format_milestone_billing_amount($milestone['calculated_amount']); ?></strong>
                                        <?php } else { ?>
                                            <span class="text-muted">-</span>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <?= get_milestone_billing_status_badge($milestone); ?>
                                    </td>
                                    <td>
                                        <?= get_milestone_billing_actions($milestone); ?>
                                    </td>
                                </tr>
                                <?php } ?>
                            <?php } else { ?>
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <?= _l('no_milestones_found'); ?>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Invoice Modal -->
<div class="modal fade" id="milestone_invoice_modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?= _l('milestone_billing_create_invoice'); ?></h4>
            </div>
            <?= form_open(admin_url('milestone_billing/create_milestone_invoice/' . $project_id), ['id' => 'milestone_invoice_form']); ?>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    <?= _l('milestone_billing_select_milestones'); ?>
                </div>
                
                <div id="selected_milestones_container">
                    <!-- Selected milestones will be populated here -->
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <?= render_date_input('invoice_date', 'invoice_date', _d(date('Y-m-d'))); ?>
                    </div>
                    <div class="col-md-6">
                        <?= render_date_input('invoice_duedate', 'invoice_due_date', _d(date('Y-m-d', strtotime('+30 days')))); ?>
                    </div>
                </div>
                
                <?= render_textarea('invoice_note', 'note', '', ['rows' => 3]); ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button type="submit" class="btn btn-primary" id="create_invoice_btn" disabled>
                    <i class="fa fa-file-invoice"></i> <?= _l('milestone_billing_create_invoice'); ?>
                </button>
            </div>
            <?= form_close(); ?>
        </div>
    </div>
</div>

<!-- Edit Milestone Billing Modal -->
<div class="modal fade" id="edit_milestone_billing_modal" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?= _l('milestone_billing_edit_billing'); ?></h4>
            </div>
            <?= form_open('#', ['id' => 'edit_milestone_billing_form']); ?>
            <div class="modal-body">
                <input type="hidden" id="edit_milestone_id" name="milestone_id">
                
                <div class="checkbox">
                    <input type="checkbox" id="edit_is_billable" name="is_billable" value="1">
                    <label for="edit_is_billable"><?= _l('milestone_is_billable_label'); ?></label>
                </div>
                
                <div id="billing_fields" style="display: none;">
                    <?= render_select('billing_type', get_milestone_billing_types(), ['id', 'name'], 'milestone_billing_type_label', '', [], [], '', '', false); ?>
                    
                    <?= render_input('rate', 'milestone_rate_label', '', 'number', ['step' => '0.01', 'min' => '0']); ?>
                    
                    <div class="alert alert-info" id="billing_type_help">
                        <!-- Help text will be populated based on selected billing type -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?= _l('close'); ?></button>
                <button type="submit" class="btn btn-primary">
                    <i class="fa fa-save"></i> <?= _l('milestone_billing_update_billing'); ?>
                </button>
            </div>
            <?= form_close(); ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#milestone-billing-table').DataTable({
        "order": [[1, "asc"]],
        "pageLength": 25,
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] }
        ]
    });
    
    // Handle select all checkbox
    $('#select_all_milestones').change(function() {
        $('input[name="milestone_ids[]"]').prop('checked', this.checked);
        updateInvoiceButton();
    });
    
    // Handle individual milestone selection
    $(document).on('change', 'input[name="milestone_ids[]"]', function() {
        updateInvoiceButton();
        updateSelectAllCheckbox();
    });
    
    // Handle billing type change in edit modal
    $('#billing_type').change(function() {
        updateBillingTypeHelp($(this).val());
    });
    
    // Handle is_billable checkbox in edit modal
    $('#edit_is_billable').change(function() {
        if ($(this).is(':checked')) {
            $('#billing_fields').show();
        } else {
            $('#billing_fields').hide();
        }
    });
    
    // Handle edit milestone billing form submission
    $('#edit_milestone_billing_form').submit(function(e) {
        e.preventDefault();
        
        var milestoneId = $('#edit_milestone_id').val();
        var formData = $(this).serialize();
        
        $.post(admin_url + 'milestone_billing/update_milestone_billing/' + milestoneId, formData)
            .done(function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    $('#edit_milestone_billing_modal').modal('hide');
                    location.reload(); // Refresh the page to show updated data
                } else {
                    alert('<?= _l('milestone_billing_update_failed'); ?>');
                }
            })
            .fail(function() {
                alert('<?= _l('milestone_billing_update_failed'); ?>');
            });
    });
});

function open_milestone_invoice_modal() {
    var selectedMilestones = $('input[name="milestone_ids[]"]:checked');
    
    if (selectedMilestones.length === 0) {
        alert('<?= _l('milestone_billing_no_milestones_selected'); ?>');
        return;
    }
    
    // Populate selected milestones in modal
    var container = $('#selected_milestones_container');
    container.empty();
    
    selectedMilestones.each(function() {
        var milestoneId = $(this).val();
        var milestoneName = $(this).closest('tr').find('td:nth-child(2) strong').text();
        var amount = $(this).closest('tr').find('td:nth-child(7)').text();
        
        container.append('<input type="hidden" name="milestones[]" value="' + milestoneId + '">');
        container.append('<div class="selected-milestone-item">' +
                        '<i class="fa fa-check-circle text-success"></i> ' +
                        '<strong>' + milestoneName + '</strong> - ' + amount +
                        '</div>');
    });
    
    $('#milestone_invoice_modal').modal('show');
}

function updateInvoiceButton() {
    var selectedCount = $('input[name="milestone_ids[]"]:checked').length;
    $('#create_invoice_btn').prop('disabled', selectedCount === 0);
}

function updateSelectAllCheckbox() {
    var totalCheckboxes = $('input[name="milestone_ids[]"]').length;
    var checkedCheckboxes = $('input[name="milestone_ids[]"]:checked').length;
    
    $('#select_all_milestones').prop('checked', totalCheckboxes > 0 && checkedCheckboxes === totalCheckboxes);
}

function edit_milestone_billing(milestoneId) {
    // Get milestone data from the table row
    var row = $('tr[data-milestone-id="' + milestoneId + '"]');
    
    // Set milestone ID
    $('#edit_milestone_id').val(milestoneId);
    
    // You would typically load the current values via AJAX here
    // For now, we'll reset the form
    $('#edit_milestone_billing_form')[0].reset();
    $('#billing_fields').hide();
    
    $('#edit_milestone_billing_modal').modal('show');
}

function updateBillingTypeHelp(billingType) {
    var helpText = '';
    switch(billingType) {
        case '1':
            helpText = '<?= _l('milestone_billing_help_fixed_rate'); ?>';
            break;
        case '2':
            helpText = '<?= _l('milestone_billing_help_percentage'); ?>';
            break;
        case '3':
            helpText = '<?= _l('milestone_billing_help_hourly'); ?>';
            break;
    }
    $('#billing_type_help').text(helpText);
}

function create_milestone_invoice(milestoneId) {
    // Select the specific milestone and open invoice modal
    $('input[name="milestone_ids[]"]').prop('checked', false);
    $('#milestone_' + milestoneId).prop('checked', true);
    updateInvoiceButton();
    open_milestone_invoice_modal();
}

function unmark_milestone_billed(milestoneId) {
    if (confirm('<?= _l('are_you_sure'); ?>')) {
        $.post(admin_url + 'milestone_billing/unmark_milestone_billed/' + milestoneId)
            .done(function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    location.reload();
                } else {
                    alert('<?= _l('milestone_billing_removal_failed'); ?>');
                }
            });
    }
}
</script>

<style>
.selected-milestone-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.selected-milestone-item:last-child {
    border-bottom: none;
}

#milestone-billing-widget .info-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

#milestone-billing-widget .info-item:last-child {
    border-bottom: none;
}

#milestone-billing-widget .info-label {
    font-weight: 500;
}

#milestone-billing-widget .info-value {
    font-weight: bold;
}
</style>
