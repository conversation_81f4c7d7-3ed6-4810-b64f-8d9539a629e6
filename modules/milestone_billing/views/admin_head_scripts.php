<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<script>
$(document).ready(function() {
    // Check if we're on a project page and milestone modal exists
    if (typeof milestone_billing_module_loaded !== 'undefined' && $('#milestone').length > 0) {
        initMilestoneBillingFields();
    }
});

function initMilestoneBillingFields() {
    // Add milestone billing fields to the milestone modal
    var billingFieldsHtml = `
        <div class="milestone-billing-section">
            <hr>
            <h5><i class="fa fa-money-bill"></i> <?= _l('milestone_billing'); ?></h5>
            
            <div class="checkbox">
                <input type="checkbox" id="milestone_is_billable" name="is_billable" value="1">
                <label for="milestone_is_billable">
                    <?= _l('milestone_is_billable_label'); ?>
                    <i class="fa-regular fa-circle-question" data-toggle="tooltip" 
                       title="<?= _l('milestone_is_billable_help'); ?>"></i>
                </label>
            </div>
            
            <div id="milestone_billing_fields" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="milestone_billing_type" class="control-label"><?= _l('milestone_billing_type_label'); ?></label>
                            <select name="milestone_billing_type" id="milestone_billing_type" class="form-control selectpicker" data-none-selected-text="<?= _l('dropdown_non_selected_tex'); ?>">
                                <option value=""><?= _l('dropdown_non_selected_tex'); ?></option>
                                <option value="1"><?= _l('milestone_billing_fixed_rate'); ?></option>
                                <option value="2"><?= _l('milestone_billing_percentage'); ?></option>
                                <option value="3"><?= _l('milestone_billing_hourly'); ?></option>
                            </select>
                            <small class="text-muted"><?= _l('milestone_billing_type_help'); ?></small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="milestone_rate" class="control-label"><?= _l('milestone_rate_label'); ?></label>
                            <input type="number" id="milestone_rate" name="milestone_rate" class="form-control" step="0.01" min="0">
                            <small class="text-muted" id="rate_help_text"><?= _l('milestone_rate_help'); ?></small>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info" id="billing_type_description" style="display: none;">
                    <!-- Billing type description will be populated here -->
                </div>
            </div>
        </div>
    `;
    
    // Insert the billing fields before the modal footer
    $('#milestone .modal-body .row .col-md-12').append(billingFieldsHtml);
    
    // Initialize event handlers
    setupMilestoneBillingEventHandlers();
}

function setupMilestoneBillingEventHandlers() {
    // Handle is_billable checkbox change
    $(document).on('change', '#milestone_is_billable', function() {
        if ($(this).is(':checked')) {
            $('#milestone_billing_fields').slideDown();
            // Set default billing type if none selected
            if (!$('#milestone_billing_type').val()) {
                $('#milestone_billing_type').val('1').trigger('change');
            }
        } else {
            $('#milestone_billing_fields').slideUp();
        }
    });
    
    // Handle billing type change
    $(document).on('change', '#milestone_billing_type', function() {
        var billingType = $(this).val();
        updateBillingTypeDescription(billingType);
        updateRateHelpText(billingType);
    });
    
    // Handle milestone form submission
    $(document).on('submit', '#milestone_form', function(e) {
        if ($('#milestone_is_billable').is(':checked')) {
            var billingType = $('#milestone_billing_type').val();
            var rate = parseFloat($('#milestone_rate').val());
            
            if (!billingType) {
                e.preventDefault();
                alert('<?= _l('milestone_billing_type_required'); ?>');
                return false;
            }
            
            if (!rate || rate <= 0) {
                e.preventDefault();
                alert('<?= _l('milestone_billing_rate_required'); ?>');
                return false;
            }
            
            if (billingType === '2' && (rate < 0 || rate > 100)) {
                e.preventDefault();
                alert('<?= _l('milestone_billing_invalid_percentage'); ?>');
                return false;
            }
        }
    });
    
    // Handle milestone modal show event to load existing data
    $(document).on('shown.bs.modal', '#milestone', function() {
        // If editing, load existing milestone billing data
        var milestoneId = $('input[name="id"]').val();
        if (milestoneId) {
            loadMilestoneBillingData(milestoneId);
        } else {
            // Reset form for new milestone
            resetMilestoneBillingForm();
        }
    });
}

function updateBillingTypeDescription(billingType) {
    var description = '';
    var rateLabel = '<?= _l('milestone_rate_label'); ?>';
    
    switch(billingType) {
        case '1': // Fixed Rate
            description = '<?= _l('milestone_billing_fixed_rate_desc'); ?>';
            rateLabel = '<?= _l('milestone_billing_fixed_rate'); ?> (<?= get_base_currency()->symbol; ?>)';
            break;
        case '2': // Percentage
            description = '<?= _l('milestone_billing_percentage_desc'); ?>';
            rateLabel = '<?= _l('milestone_billing_percentage'); ?> (%)';
            break;
        case '3': // Hourly
            description = '<?= _l('milestone_billing_hourly_desc'); ?>';
            rateLabel = '<?= _l('milestone_billing_hourly'); ?> (<?= get_base_currency()->symbol; ?>/hr)';
            break;
    }
    
    if (description) {
        $('#billing_type_description').html('<i class="fa fa-info-circle"></i> ' + description).show();
    } else {
        $('#billing_type_description').hide();
    }
    
    // Update rate field label
    $('label[for="milestone_rate"]').html(rateLabel + ' <span class="text-danger">*</span>');
}

function updateRateHelpText(billingType) {
    var helpText = '';
    
    switch(billingType) {
        case '1':
            helpText = '<?= _l('milestone_billing_help_fixed_rate'); ?>';
            $('#milestone_rate').removeAttr('max');
            break;
        case '2':
            helpText = '<?= _l('milestone_billing_help_percentage'); ?>';
            $('#milestone_rate').attr('max', '100');
            break;
        case '3':
            helpText = '<?= _l('milestone_billing_help_hourly'); ?>';
            $('#milestone_rate').removeAttr('max');
            break;
        default:
            helpText = '<?= _l('milestone_rate_help'); ?>';
            $('#milestone_rate').removeAttr('max');
    }
    
    $('#rate_help_text').text(helpText);
}

function loadMilestoneBillingData(milestoneId) {
    // Load existing milestone billing data via AJAX
    $.get(admin_url + 'milestone_billing/get_milestone_billing_data/' + milestoneId)
        .done(function(response) {
            var data = JSON.parse(response);
            if (data.success) {
                $('#milestone_is_billable').prop('checked', data.is_billable == 1);
                $('#milestone_billing_type').val(data.billing_type);
                $('#milestone_rate').val(data.rate);
                
                if (data.is_billable == 1) {
                    $('#milestone_billing_fields').show();
                    updateBillingTypeDescription(data.billing_type);
                    updateRateHelpText(data.billing_type);
                }
            }
        })
        .fail(function() {
            console.log('Failed to load milestone billing data');
        });
}

function resetMilestoneBillingForm() {
    $('#milestone_is_billable').prop('checked', false);
    $('#milestone_billing_type').val('');
    $('#milestone_rate').val('');
    $('#milestone_billing_fields').hide();
    $('#billing_type_description').hide();
}
</script>

<style>
.milestone-billing-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
}

.milestone-billing-section h5 {
    color: #333;
    margin-bottom: 15px;
}

#milestone_billing_fields {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

#billing_type_description {
    margin-top: 10px;
    margin-bottom: 0;
}

.text-danger {
    color: #d9534f;
}
</style>
