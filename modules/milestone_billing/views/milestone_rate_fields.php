<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<div class="milestone-billing-section">
    <hr>
    <h5><i class="fa fa-money-bill"></i> <?= _l('milestone_billing'); ?></h5>
    
    <div class="checkbox">
        <input type="checkbox" id="milestone_is_billable" name="is_billable" value="1">
        <label for="milestone_is_billable">
            <?= _l('milestone_is_billable_label'); ?>
            <i class="fa-regular fa-circle-question" data-toggle="tooltip" 
               title="<?= _l('milestone_is_billable_help'); ?>"></i>
        </label>
    </div>
    
    <div id="milestone_billing_fields" style="display: none;">
        <div class="row">
            <div class="col-md-6">
                <?= render_select('milestone_billing_type', get_milestone_billing_types(), ['id', 'name'], 'milestone_billing_type_label', '', [], [], '', '', false); ?>
                <small class="text-muted"><?= _l('milestone_billing_type_help'); ?></small>
            </div>
            <div class="col-md-6">
                <?= render_input('milestone_rate', 'milestone_rate_label', '', 'number', ['step' => '0.01', 'min' => '0']); ?>
                <small class="text-muted" id="rate_help_text"><?= _l('milestone_rate_help'); ?></small>
            </div>
        </div>
        
        <div class="alert alert-info" id="billing_type_description" style="display: none;">
            <!-- Billing type description will be populated here -->
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Handle is_billable checkbox change
    $('#milestone_is_billable').change(function() {
        if ($(this).is(':checked')) {
            $('#milestone_billing_fields').slideDown();
            // Set default billing type if none selected
            if (!$('#milestone_billing_type').val()) {
                $('#milestone_billing_type').val('1').trigger('change');
            }
        } else {
            $('#milestone_billing_fields').slideUp();
        }
    });
    
    // Handle billing type change
    $('#milestone_billing_type').change(function() {
        var billingType = $(this).val();
        updateBillingTypeDescription(billingType);
        updateRateHelpText(billingType);
    });
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});

function updateBillingTypeDescription(billingType) {
    var description = '';
    var rateLabel = '<?= _l('milestone_rate_label'); ?>';
    
    switch(billingType) {
        case '1': // Fixed Rate
            description = '<?= _l('milestone_billing_fixed_rate_desc'); ?>';
            rateLabel = '<?= _l('milestone_billing_fixed_rate'); ?> (<?= get_base_currency()->symbol; ?>)';
            break;
        case '2': // Percentage
            description = '<?= _l('milestone_billing_percentage_desc'); ?>';
            rateLabel = '<?= _l('milestone_billing_percentage'); ?> (%)';
            break;
        case '3': // Hourly
            description = '<?= _l('milestone_billing_hourly_desc'); ?>';
            rateLabel = '<?= _l('milestone_billing_hourly'); ?> (<?= get_base_currency()->symbol; ?>/hr)';
            break;
    }
    
    if (description) {
        $('#billing_type_description').html('<i class="fa fa-info-circle"></i> ' + description).show();
    } else {
        $('#billing_type_description').hide();
    }
    
    // Update rate field label
    $('label[for="milestone_rate"]').html(rateLabel + ' <span class="text-danger">*</span>');
}

function updateRateHelpText(billingType) {
    var helpText = '';
    
    switch(billingType) {
        case '1':
            helpText = '<?= _l('milestone_billing_help_fixed_rate'); ?>';
            break;
        case '2':
            helpText = '<?= _l('milestone_billing_help_percentage'); ?>';
            $('#milestone_rate').attr('max', '100');
            break;
        case '3':
            helpText = '<?= _l('milestone_billing_help_hourly'); ?>';
            $('#milestone_rate').removeAttr('max');
            break;
        default:
            helpText = '<?= _l('milestone_rate_help'); ?>';
            $('#milestone_rate').removeAttr('max');
    }
    
    $('#rate_help_text').text(helpText);
}

// Form validation
$('#milestone_form').on('submit', function(e) {
    if ($('#milestone_is_billable').is(':checked')) {
        var billingType = $('#milestone_billing_type').val();
        var rate = parseFloat($('#milestone_rate').val());
        
        if (!billingType) {
            e.preventDefault();
            alert('<?= _l('milestone_billing_type_required'); ?>');
            return false;
        }
        
        if (!rate || rate <= 0) {
            e.preventDefault();
            alert('<?= _l('milestone_billing_rate_required'); ?>');
            return false;
        }
        
        if (billingType === '2' && (rate < 0 || rate > 100)) {
            e.preventDefault();
            alert('<?= _l('milestone_billing_invalid_percentage'); ?>');
            return false;
        }
    }
});
</script>

<style>
.milestone-billing-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
}

.milestone-billing-section h5 {
    color: #333;
    margin-bottom: 15px;
}

#milestone_billing_fields {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

#billing_type_description {
    margin-top: 10px;
    margin-bottom: 0;
}

.text-danger {
    color: #d9534f;
}
</style>
