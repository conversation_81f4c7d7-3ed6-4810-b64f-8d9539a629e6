<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<script>
// Additional project-specific scripts for milestone billing
$(document).ready(function() {
    // Override the original milestone form submission to handle billing data
    if (typeof milestone_billing_module_loaded !== 'undefined') {
        interceptMilestoneFormSubmission();
    }
});

function interceptMilestoneFormSubmission() {
    // Store the original form action
    var originalAction = $('#milestone_form').attr('action');
    
    // Override form submission
    $('#milestone_form').off('submit').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var isEdit = $('input[name="id"]').val() ? true : false;
        
        // Submit to our custom handler first
        $.post(admin_url + 'milestone_billing/handle_milestone_save', formData)
            .done(function(response) {
                var result = JSON.parse(response);
                if (result.success) {
                    // Now submit to original handler
                    submitOriginalMilestoneForm(originalAction, formData);
                } else {
                    alert(result.message || 'Failed to save milestone billing data');
                }
            })
            .fail(function() {
                // If our handler fails, still try to submit the original form
                submitOriginalMilestoneForm(originalAction, formData);
            });
    });
}

function submitOriginalMilestoneForm(action, formData) {
    // Create a temporary form to submit to the original action
    var tempForm = $('<form>', {
        'method': 'POST',
        'action': action
    });
    
    // Add all form data as hidden inputs
    var params = new URLSearchParams(formData);
    for (var pair of params.entries()) {
        tempForm.append($('<input>', {
            'type': 'hidden',
            'name': pair[0],
            'value': pair[1]
        }));
    }
    
    // Submit the form
    $('body').append(tempForm);
    tempForm.submit();
}
</script>
