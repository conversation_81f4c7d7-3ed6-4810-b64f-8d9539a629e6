<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>

<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-money-bill"></i> <?= $title; ?>
                        </h3>
                    </div>
                    <div class="panel-body">
                        
                        <!-- Statistics Cards -->
                        <div class="row" id="milestone-billing-stats">
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-blue">
                                        <i class="fa fa-tasks"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= _l('milestone_billing_stats_total_projects'); ?></span>
                                        <span class="info-box-number" id="total-projects">-</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-green">
                                        <i class="fa fa-dollar-sign"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= _l('milestone_billing_stats_total_revenue'); ?></span>
                                        <span class="info-box-number" id="total-revenue">-</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-yellow">
                                        <i class="fa fa-clock"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= _l('milestone_billing_stats_pending_revenue'); ?></span>
                                        <span class="info-box-number" id="pending-revenue">-</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-red">
                                        <i class="fa fa-percentage"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= _l('milestone_billing_stats_completion_rate'); ?></span>
                                        <span class="info-box-number" id="completion-rate">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#filters-collapse">
                                                <i class="fa fa-filter"></i> <?= _l('filters'); ?>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="filters-collapse" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <?= render_select('filter_project', [], ['id', 'name'], 'project', '', [], [], '', '', false); ?>
                                                </div>
                                                <div class="col-md-3">
                                                    <?= render_select('filter_status', [
                                                        ['id' => 'billable', 'name' => _l('milestone_billing_progress_in_progress')],
                                                        ['id' => 'completed', 'name' => _l('milestone_billing_progress_completed')],
                                                        ['id' => 'billed', 'name' => _l('milestone_billing_progress_billed')]
                                                    ], ['id', 'name'], 'status', '', [], [], '', '', false); ?>
                                                </div>
                                                <div class="col-md-3">
                                                    <?= render_select('filter_billing_type', get_milestone_billing_types(), ['id', 'name'], 'milestone_billing_type', '', [], [], '', '', false); ?>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>&nbsp;</label>
                                                        <div>
                                                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                                                <i class="fa fa-search"></i> <?= _l('apply_filters'); ?>
                                                            </button>
                                                            <button type="button" class="btn btn-default" onclick="clearFilters()">
                                                                <i class="fa fa-times"></i> <?= _l('clear_filters'); ?>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered" id="milestone-billing-datatable">
                                <thead>
                                    <tr>
                                        <th><?= _l('project'); ?></th>
                                        <th><?= _l('milestone_billing_milestone_name'); ?></th>
                                        <th><?= _l('milestone_billing_due_date'); ?></th>
                                        <th><?= _l('milestone_billing_type'); ?></th>
                                        <th><?= _l('milestone_billing_rate'); ?></th>
                                        <th><?= _l('milestone_billing_completion'); ?></th>
                                        <th><?= _l('milestone_billing_calculated_amount'); ?></th>
                                        <th><?= _l('milestone_billing_status'); ?></th>
                                        <th><?= _l('options'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php init_tail(); ?>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#milestone-billing-datatable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": admin_url + "milestone_billing/table",
            "type": "POST",
            "data": function(d) {
                d.filter_project = $('#filter_project').val();
                d.filter_status = $('#filter_status').val();
                d.filter_billing_type = $('#filter_billing_type').val();
            }
        },
        "columns": [
            {"data": "project_name"},
            {"data": "milestone_name"},
            {"data": "due_date"},
            {"data": "billing_type"},
            {"data": "rate"},
            {"data": "completion"},
            {"data": "calculated_amount"},
            {"data": "status"},
            {"data": "actions", "orderable": false}
        ],
        "order": [[2, "asc"]],
        "pageLength": 25,
        "responsive": true
    });
    
    // Load statistics
    loadStatistics();
    
    // Load projects for filter
    loadProjectsFilter();
});

function loadStatistics() {
    $.get(admin_url + 'milestone_billing/get_statistics')
        .done(function(response) {
            var stats = JSON.parse(response);
            
            $('#total-projects').text(stats.total_projects || 0);
            $('#total-revenue').text(stats.total_revenue_formatted || app_format_money(0));
            $('#pending-revenue').text(stats.pending_revenue_formatted || app_format_money(0));
            $('#completion-rate').text((stats.completion_rate || 0) + '%');
        })
        .fail(function() {
            console.log('Failed to load statistics');
        });
}

function loadProjectsFilter() {
    $.get(admin_url + 'projects/get_projects_for_filter')
        .done(function(response) {
            var projects = JSON.parse(response);
            var options = '<option value=""><?= _l('all_projects'); ?></option>';
            
            projects.forEach(function(project) {
                options += '<option value="' + project.id + '">' + project.name + '</option>';
            });
            
            $('#filter_project').html(options);
        })
        .fail(function() {
            console.log('Failed to load projects for filter');
        });
}

function applyFilters() {
    $('#milestone-billing-datatable').DataTable().ajax.reload();
}

function clearFilters() {
    $('#filter_project').val('');
    $('#filter_status').val('');
    $('#filter_billing_type').val('');
    applyFilters();
}

function viewMilestoneBilling(projectId) {
    window.location.href = admin_url + 'projects/view/' + projectId + '?group=milestone_billing';
}

function editMilestoneBilling(milestoneId) {
    // Implementation for editing milestone billing
    // This could open a modal or redirect to edit page
    console.log('Edit milestone billing:', milestoneId);
}

function createInvoiceFromMilestone(milestoneId) {
    // Implementation for creating invoice from single milestone
    console.log('Create invoice from milestone:', milestoneId);
}
</script>

<style>
.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
}

.info-box-icon > i {
    color: #fff;
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.info-box-text {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 13px;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.bg-blue { background-color: #3c8dbc !important; }
.bg-green { background-color: #00a65a !important; }
.bg-yellow { background-color: #f39c12 !important; }
.bg-red { background-color: #dd4b39 !important; }

#filters-collapse .panel-body {
    background-color: #f9f9f9;
}
</style>
