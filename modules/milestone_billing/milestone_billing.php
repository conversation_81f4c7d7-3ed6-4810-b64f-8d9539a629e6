<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Milestone Billing
Description: Add rate field to milestones and invoice based on milestones
Version: 1.0.0
Requires at least: 2.3.*
*/

define('MILESTONE_BILLING_MODULE_NAME', 'milestone_billing');

/**
 * Register activation module hook
 */
register_activation_hook(MILESTONE_BILLING_MODULE_NAME, 'milestone_billing_activation_hook');

function milestone_billing_activation_hook()
{
    require_once __DIR__ . '/install.php';
}

/**
 * Register language files
 */
register_language_files(MILESTONE_BILLING_MODULE_NAME, [MILESTONE_BILLING_MODULE_NAME]);

// Only add hooks if we're not in the activation process
if (!defined('MODULE_ACTIVATION_IN_PROGRESS')) {
    hooks()->add_action('modules_loaded', 'milestone_billing_init_hooks');
}

function milestone_billing_init_hooks()
{
    // Add JavaScript to inject rate field into milestone form
    hooks()->add_action('app_admin_head', 'milestone_billing_add_scripts');

    // Add milestone options to invoice generation
    hooks()->add_action('before_project_invoice_form', 'milestone_billing_add_invoice_options');

    // Hook into admin_init to override milestone handling
    hooks()->add_action('admin_init', 'milestone_billing_override_milestone_handling');
}

/**
 * Override milestone handling to save rate data
 */
function milestone_billing_override_milestone_handling()
{
    // Remove the complex override logic that might be causing 500 errors
    // We'll handle this differently using AJAX
}

/**
 * Add scripts to admin head for milestone form enhancement
 */
function milestone_billing_add_scripts()
{
    $CI = &get_instance();

    // Only add on project pages
    if ($CI->router->fetch_class() == 'projects') {
        echo '<script type="text/javascript">
        console.log("Milestone Billing: Script loaded");

        // Wait for jQuery to be available
        function waitForJQuery(callback) {
            if (typeof jQuery !== "undefined") {
                callback(jQuery);
            } else {
                setTimeout(function() {
                    waitForJQuery(callback);
                }, 100);
            }
        }

        waitForJQuery(function($) {
            console.log("Milestone Billing: jQuery ready");

            $(document).ready(function() {
                console.log("Milestone Billing: Document ready");

                // Function to add rate field to milestone form
                function addMilestoneRateField() {
                    console.log("Milestone Billing: Trying to add rate field");
                    console.log("Milestone modal exists:", $("#milestone").length > 0);
                    console.log("Rate field exists:", $("#milestone_rate").length > 0);

                    if ($("#milestone").length > 0 && $("#milestone_rate").length === 0) {
                        console.log("Milestone Billing: Adding rate field");
                        var rateField = \'<div class="form-group">\' +
                            \'<label for="milestone_rate" class="control-label">Rate</label>\' +
                            \'<input type="number" id="milestone_rate" name="milestone_rate" class="form-control" step="0.01" min="0" placeholder="Enter milestone rate">\' +
                            \'<small class="text-muted">Optional: Set a rate for this milestone to include in invoicing</small>\' +
                            \'</div>\';
                        $("#milestone .modal-body").append(rateField);
                        console.log("Milestone Billing: Rate field added");
                    }
                }

                // Add rate field when milestone modal is shown
                $(document).on("shown.bs.modal", "#milestone", function() {
                    console.log("Milestone Billing: Modal shown event");
                    setTimeout(function() {
                        addMilestoneRateField();

                        var milestoneId = $("#milestone input[name=\'id\']").val();
                        console.log("Milestone ID:", milestoneId);

                        if (milestoneId && milestoneId !== "") {
                            // Load existing rate
                            $.get(admin_url + "milestone_billing/get_milestone_rate/" + milestoneId)
                                .done(function(response) {
                                    try {
                                        var data = JSON.parse(response);
                                        if (data.rate) {
                                            $("#milestone_rate").val(data.rate);
                                            console.log("Milestone Billing: Loaded rate:", data.rate);
                                        }
                                    } catch(e) {
                                        console.log("Error parsing milestone rate response:", e);
                                    }
                                })
                                .fail(function() {
                                    console.log("Failed to load milestone rate");
                                });
                        } else {
                            $("#milestone_rate").val("");
                        }
                    }, 100);
                });

                // Also add field when milestone modal content is loaded
                $(document).on("click", "[data-toggle=\'modal\'][data-target=\'#milestone\']", function() {
                    console.log("Milestone Billing: Modal trigger clicked");
                    setTimeout(function() {
                        addMilestoneRateField();
                    }, 200);
                });

                // Try to add field when page loads (in case modal is already there)
                setTimeout(function() {
                    addMilestoneRateField();
                }, 1000);

                // Handle form submission to save rate after milestone is saved
                $(document).on("submit", "#milestone_form", function(e) {
                    var rate = $("#milestone_rate").val();
                    if (rate && rate !== "") {
                        // Store rate to save after form submission
                        window.pendingMilestoneRate = rate;
                        window.pendingMilestoneId = $("#milestone input[name=\'id\']").val();

                        // Set a timeout to save the rate after the form is submitted
                        setTimeout(function() {
                            saveMilestoneRate();
                        }, 1000);
                    }
                });

                function saveMilestoneRate() {
                    if (window.pendingMilestoneRate) {
                        var milestoneId = window.pendingMilestoneId;

                        // If no milestone ID (new milestone), try to get the latest one
                        if (!milestoneId) {
                            // For new milestones, we need to find the newly created milestone
                            // This is a simple approach - in production you might want something more robust
                            setTimeout(function() {
                                $.post(admin_url + "milestone_billing/save_rate_for_latest_milestone", {
                                    rate: window.pendingMilestoneRate
                                }).done(function() {
                                    console.log("Milestone rate saved for new milestone");
                                }).fail(function() {
                                    console.log("Failed to save milestone rate for new milestone");
                                });

                                window.pendingMilestoneRate = null;
                                window.pendingMilestoneId = null;
                            }, 500);
                        } else {
                            // For existing milestones, save directly
                            $.post(admin_url + "milestone_billing/save_milestone_rate", {
                                milestone_id: milestoneId,
                                rate: window.pendingMilestoneRate
                            }).done(function() {
                                console.log("Milestone rate saved for existing milestone");
                            }).fail(function() {
                                console.log("Failed to save milestone rate for existing milestone");
                            });

                            window.pendingMilestoneRate = null;
                            window.pendingMilestoneId = null;
                        }
                    }
                }
            });
        });
        </script>';
    }
}

/**
 * Add milestone options to project invoice form
 */
function milestone_billing_add_invoice_options($project_id)
{
    $CI = &get_instance();
    $CI->load->model('milestone_billing/milestone_billing_model');

    $milestones = $CI->milestone_billing_model->get_project_milestones_with_rates($project_id);

    if (!empty($milestones)) {
        echo '<hr>
        <div class="milestone-billing-section">
            <div class="row">
                <div class="col-md-10">
                    <div class="radio radio-primary">
                        <input type="radio" name="invoice_data_type" value="milestones" id="invoice_milestones">
                        <label for="invoice_milestones">Invoice based on milestones</label>
                    </div>
                </div>
            </div>

            <div id="milestone_invoice_options" style="display: none; margin-top: 15px;">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    Select milestones to include in the invoice
                </div>';

        foreach ($milestones as $milestone) {
            if ($milestone['rate'] > 0) {
                echo '<div class="checkbox checkbox-default">
                    <input type="checkbox" name="milestone_ids[]" value="' . $milestone['id'] . '" id="milestone_' . $milestone['id'] . '">
                    <label for="milestone_' . $milestone['id'] . '">
                        <strong>' . e($milestone['name']) . '</strong> - ' . app_format_money($milestone['rate']) . '
                        <br><small class="text-muted">Due: ' . ($milestone['due_date'] ? _d($milestone['due_date']) : 'No due date') . '</small>
                    </label>
                </div>';
            }
        }

        echo '</div>
        </div>

        <script type="text/javascript">
        $(document).ready(function() {
            $("input[name=\'invoice_data_type\']").change(function() {
                if ($(this).val() === "milestones" && $(this).is(":checked")) {
                    $("#milestone_invoice_options").slideDown();
                } else {
                    $("#milestone_invoice_options").slideUp();
                }
            });

            // Store original function if it exists
            if (typeof window.invoice_project !== "undefined") {
                window.original_invoice_project = window.invoice_project;
            }

            // Override the invoice_project function to handle milestone billing
            window.invoice_project = function(project_id) {
                var invoiceType = $("input[name=\'invoice_data_type\']:checked").val();

                if (invoiceType === "milestones") {
                    var selectedMilestones = $("input[name=\'milestone_ids[]\']:checked");

                    if (selectedMilestones.length === 0) {
                        alert("Please select at least one milestone");
                        return false;
                    }

                    // Create form and submit to milestone billing controller
                    var form = $("<form>", {
                        method: "POST",
                        action: admin_url + "milestone_billing/create_milestone_invoice/" + project_id
                    });

                    selectedMilestones.each(function() {
                        form.append($("<input>", {
                            type: "hidden",
                            name: "milestone_ids[]",
                            value: $(this).val()
                        }));
                    });

                    $("body").append(form);
                    form.submit();
                    return false;
                } else {
                    // Call original function for other invoice types
                    if (typeof window.original_invoice_project === "function") {
                        return window.original_invoice_project(project_id);
                    }
                }
            };
        });
        </script>';
    }
}


