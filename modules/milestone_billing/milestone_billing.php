<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Milestone Billing
Description: Module for adding rates to milestones and creating milestone-based invoices
Version: 1.0.0
Requires at least: 2.3.*
*/

define('MILESTONE_BILLING_MODULE_NAME', 'milestone_billing');

/**
 * Register activation module hook
 */
register_activation_hook(MILESTONE_BILLING_MODULE_NAME, 'milestone_billing_activation_hook');

function milestone_billing_activation_hook()
{
    require_once __DIR__ . '/install.php';
}

/**
 * Register language files, must be registered if the module is using languages
 */
register_language_files(MILESTONE_BILLING_MODULE_NAME, [MILESTONE_BILLING_MODULE_NAME]);

// Only add hooks if we're not in the activation process
if (!defined('MODULE_ACTIVATION_IN_PROGRESS')) {
    /**
     * Init milestone billing module hooks - delayed to ensure all functions are available
     */
    hooks()->add_action('modules_loaded', 'milestone_billing_init_hooks');
}

function milestone_billing_init_hooks()
{
    // Load helper functions
    if (file_exists(__DIR__ . '/helpers/milestone_billing_helper.php')) {
        require_once __DIR__ . '/helpers/milestone_billing_helper.php';
    }
    
    // Add admin hooks
    hooks()->add_action('admin_init', 'milestone_billing_module_init_menu_items');
    hooks()->add_action('admin_init', 'milestone_billing_permissions');
    hooks()->add_action('admin_init', 'milestone_billing_init_project_tab');
    
    // Hook into admin head to add JavaScript for milestone form enhancement
    hooks()->add_action('app_admin_head', 'milestone_billing_add_admin_head');

    // Hook into project view to add milestone billing functionality
    hooks()->add_action('after_project_view_as_client', 'milestone_billing_add_project_scripts');

    // Hook into project invoice modal to add milestone options
    hooks()->add_action('before_project_invoice_form', 'milestone_billing_add_invoice_options');

    // Hook to process pending milestone billing data after milestone creation
    hooks()->add_action('after_milestone_added', 'milestone_billing_process_pending_data');
}

/**
 * Add Milestone Billing tab to projects
 */
function milestone_billing_init_project_tab()
{
    if (!function_exists('get_instance')) {
        return;
    }
    
    $CI = &get_instance();
    
    if (!isset($CI->app_tabs)) {
        return;
    }
    
    $CI->app_tabs->add_project_tab('milestone_billing', [
        'name'     => function_exists('_l') ? _l('milestone_billing') : 'Milestone Billing',
        'icon'     => 'fa fa-money-bill',
        'view'     => 'milestone_billing/project_milestone_billing',
        'position' => 50,
    ]);
}

/**
 * Register staff capabilities for milestone billing
 */
function milestone_billing_permissions()
{
    if (!function_exists('register_staff_capabilities')) {
        return;
    }
    
    $capabilities = [];

    $capabilities['capabilities'] = [
        'view'   => function_exists('_l') ? _l('permission_view') . '(' . _l('permission_global') . ')' : 'View (Global)',
        'create' => function_exists('_l') ? _l('permission_create') : 'Create',
        'edit'   => function_exists('_l') ? _l('permission_edit') : 'Edit',
        'delete' => function_exists('_l') ? _l('permission_delete') : 'Delete',
    ];

    $name = function_exists('_l') ? _l('milestone_billing') : 'Milestone Billing';
    register_staff_capabilities('milestone_billing', $capabilities, $name);
}

/**
 * Init milestone billing module menu items in admin_init hook
 * @return null
 */
function milestone_billing_module_init_menu_items()
{
    if (!function_exists('get_instance') || !function_exists('staff_can') || !function_exists('admin_url')) {
        return;
    }
    
    $CI = &get_instance();
    
    if (!isset($CI->app_menu)) {
        return;
    }

    if (staff_can('view', 'milestone_billing')) {
        $CI->app_menu->add_sidebar_children_item('projects', [
            'slug'     => 'milestone-billing',
            'name'     => function_exists('_l') ? _l('milestone_billing') : 'Milestone Billing',
            'href'     => admin_url('milestone_billing'),
            'position' => 30,
        ]);
    }
}

/**
 * Add admin head content for milestone billing
 */
function milestone_billing_add_admin_head()
{
    if (!function_exists('get_instance')) {
        return;
    }

    $CI = &get_instance();

    // Only add on project pages
    if ($CI->router->fetch_class() == 'projects') {
        echo '<script>var milestone_billing_module_loaded = true;</script>';
        $CI->load->view('milestone_billing/admin_head_scripts');
    }
}

/**
 * Add project scripts for milestone billing
 */
function milestone_billing_add_project_scripts()
{
    if (!function_exists('get_instance')) {
        return;
    }

    $CI = &get_instance();
    $CI->load->view('milestone_billing/project_scripts');
}

/**
 * Add milestone options to project invoice form
 */
function milestone_billing_add_invoice_options($project_id)
{
    if (!function_exists('get_instance')) {
        return;
    }

    $CI = &get_instance();

    if (!$CI->load->is_loaded('milestone_billing_model')) {
        $CI->load->model('milestone_billing/milestone_billing_model');
    }

    $data['billable_milestones'] = $CI->milestone_billing_model->get_billable_milestones($project_id);
    $CI->load->view('milestone_billing/invoice_milestone_options', $data);
}

/**
 * Process pending milestone billing data after milestone creation
 */
function milestone_billing_process_pending_data($milestone_id)
{
    if (!function_exists('get_instance')) {
        return;
    }

    $CI = &get_instance();

    if (!$CI->load->is_loaded('milestone_billing_model')) {
        $CI->load->model('milestone_billing/milestone_billing_model');
    }

    $pending_data = $CI->session->userdata('pending_milestone_billing');

    if ($pending_data && $milestone_id) {
        $CI->milestone_billing_model->update_milestone_billing_data($milestone_id, $pending_data);
        $CI->session->unset_userdata('pending_milestone_billing');
    }
}

/**
 * Get milestone billing types
 * @return array
 */
function get_milestone_billing_types()
{
    return [
        1 => [
            'id'    => 1,
            'name'  => function_exists('_l') ? _l('milestone_billing_fixed_rate') : 'Fixed Rate',
            'description' => function_exists('_l') ? _l('milestone_billing_fixed_rate_desc') : 'Bill a fixed amount when milestone is completed',
        ],
        2 => [
            'id'    => 2,
            'name'  => function_exists('_l') ? _l('milestone_billing_percentage') : 'Percentage of Project',
            'description' => function_exists('_l') ? _l('milestone_billing_percentage_desc') : 'Bill a percentage of total project value',
        ],
        3 => [
            'id'    => 3,
            'name'  => function_exists('_l') ? _l('milestone_billing_hourly') : 'Hourly Rate',
            'description' => function_exists('_l') ? _l('milestone_billing_hourly_desc') : 'Bill based on hours logged for milestone tasks',
        ],
    ];
}

/**
 * Get milestone billing type by ID
 * @param int $type_id
 * @return array|null
 */
function get_milestone_billing_type($type_id)
{
    $types = get_milestone_billing_types();
    return isset($types[$type_id]) ? $types[$type_id] : null;
}

/**
 * Format milestone billing type for display
 * @param int $type_id
 * @return string
 */
function format_milestone_billing_type($type_id)
{
    $type = get_milestone_billing_type($type_id);
    if (!$type) {
        return '';
    }
    
    return '<span class="label label-info">' . $type['name'] . '</span>';
}
