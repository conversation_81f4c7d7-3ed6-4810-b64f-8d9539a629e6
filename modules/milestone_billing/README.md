# Milestone Billing Module for Perfex CRM

## Overview

The Milestone Billing module extends Perfex CRM's project management functionality by adding comprehensive billing capabilities to project milestones. This module allows you to set rates for milestones and create invoices based on milestone completion.

## Features

### ✅ Core Functionality
- **Milestone Rate Configuration**: Add billing rates to any project milestone
- **Multiple Billing Types**: Support for fixed rate, percentage-based, and hourly billing
- **Milestone-Based Invoicing**: Create invoices directly from completed milestones
- **Project Integration**: Seamlessly integrates with existing project workflow
- **Billing Status Tracking**: Track which milestones have been billed

### ✅ Billing Types
1. **Fixed Rate**: Bill a predetermined amount when milestone is completed
2. **Percentage of Project**: Bill a percentage of the total project value
3. **Hourly Rate**: Bill based on hours logged for milestone tasks

### ✅ User Interface
- **Enhanced Milestone Form**: Billing fields automatically added to milestone creation/editing
- **Project Milestone Billing Tab**: Dedicated tab for managing milestone billing
- **Invoice Integration**: Milestone billing options in project invoice creation
- **Comprehensive Dashboard**: Overview of all milestone billing activities

### ✅ Permissions & Security
- **Role-Based Access**: Granular permissions for viewing, creating, editing, and deleting
- **Staff Capabilities**: Configurable permissions for different staff roles
- **Data Validation**: Comprehensive validation of billing data

## Installation

### 1. Upload Module
1. Copy the `milestone_billing` folder to your Perfex CRM `modules/` directory
2. Ensure proper file permissions are set

### 2. Install Module
1. Go to **Setup → Modules** in your Perfex CRM admin panel
2. Find "Milestone Billing" in the available modules list
3. Click **Install**
4. Click **Activate** after installation completes

### 3. Configure Permissions
1. Go to **Setup → Staff → Roles**
2. Edit the roles that should have access to milestone billing
3. Configure the milestone billing permissions as needed

## Usage

### Setting Up Milestone Billing

1. **Create/Edit a Milestone**:
   - Go to any project and navigate to the Milestones tab
   - Create a new milestone or edit an existing one
   - Check "Make this milestone billable"
   - Select the billing type (Fixed Rate, Percentage, or Hourly)
   - Enter the appropriate rate
   - Save the milestone

2. **Billing Types Explained**:
   - **Fixed Rate**: Enter the exact amount to bill (e.g., $1000)
   - **Percentage**: Enter percentage of project value (e.g., 25 for 25%)
   - **Hourly**: Enter hourly rate (will be multiplied by logged hours)

### Creating Milestone-Based Invoices

1. **From Project View**:
   - Go to the project's "Milestone Billing" tab
   - Select the milestones you want to bill
   - Click "Create Invoice from Milestones"
   - Review and confirm the invoice details

2. **From Project Invoice Modal**:
   - Go to project overview and click "Invoice Project"
   - Select "Invoice based on milestones" option
   - Choose the milestones to include
   - Complete the invoice creation

### Managing Milestone Billing

1. **Global Overview**:
   - Go to **Projects → Milestone Billing** in the main menu
   - View all billable milestones across projects
   - Filter by project, status, or billing type
   - Track billing statistics and revenue

2. **Project-Specific Management**:
   - Use the "Milestone Billing" tab in any project
   - View billing summary and milestone status
   - Manage individual milestone billing settings

## Database Tables

The module creates the following database tables:

### `tblmilestones` (Extended)
- `rate`: Billing rate for the milestone
- `billing_type`: Type of billing (1=Fixed, 2=Percentage, 3=Hourly)
- `is_billable`: Whether the milestone is billable
- `billed`: Whether the milestone has been billed

### `tblmilestone_billing_items`
- Tracks individual billing records
- Links milestones to invoices
- Stores billing details and amounts

### `tblmilestone_billing_settings`
- Project-specific billing settings
- Auto-billing preferences
- Default billing configurations

## Configuration Options

### Project Settings
- **Auto-bill completed milestones**: Automatically create invoices when milestones are completed
- **Require milestone completion**: Only allow billing of 100% completed milestones
- **Default billing type**: Set default billing type for new milestones
- **Default rate**: Set default rate for new milestones

### Global Settings
- **Milestone completion tracking**: Track milestone completion based on task completion
- **Billing validation**: Validate billing data before invoice creation
- **Currency handling**: Support for multi-currency billing

## Permissions

The module adds these permission categories:

- **View**: View milestone billing information
- **Create**: Create milestone billing configurations
- **Edit**: Edit milestone billing settings
- **Delete**: Delete milestone billing records

## API Integration

The module provides several API endpoints for integration:

- `GET /milestone_billing/get_statistics`: Get global billing statistics
- `GET /milestone_billing/get_project_statistics/{project_id}`: Get project-specific statistics
- `POST /milestone_billing/create_milestone_invoice/{project_id}`: Create invoice from milestones
- `PUT /milestone_billing/update_milestone_billing/{milestone_id}`: Update milestone billing settings

## Customization

### Adding Custom Fields
To add custom fields to milestone billing:

1. Modify the database schema in `install.php`
2. Update the model methods in `Milestone_billing_model.php`
3. Add form fields in the views
4. Update language files for new labels

### Custom Billing Types
To add new billing types:

1. Update the `get_milestone_billing_types()` function
2. Modify the calculation logic in the model
3. Add appropriate validation rules
4. Update the user interface

## Troubleshooting

### Common Issues

1. **Milestone billing fields not showing**:
   - Ensure the module is activated
   - Check that JavaScript is enabled
   - Verify file permissions

2. **Invoice creation fails**:
   - Check that milestones have valid billing data
   - Ensure project has a client assigned
   - Verify staff permissions

3. **Billing amounts incorrect**:
   - Verify billing type configuration
   - Check project cost settings for percentage billing
   - Ensure time tracking is accurate for hourly billing

### Debug Mode
Enable debug mode by adding this to your config:
```php
$config['milestone_billing_debug'] = true;
```

## Support

For support and feature requests:
1. Check the module documentation
2. Review the troubleshooting section
3. Contact your system administrator

## Version History

- **v1.0.0**: Initial release with core milestone billing functionality

## License

This module is licensed under the same terms as Perfex CRM.
