<?php

# Version 1.0.0

$lang['milestone_billing'] = 'Milestone Billing';
$lang['milestone_billing_rate'] = 'Rate';
$lang['milestone_billing_type'] = 'Billing Type';
$lang['milestone_billing_is_billable'] = 'Is Billable';
$lang['milestone_billing_billed'] = 'Billed';
$lang['milestone_billing_amount'] = 'Amount';
$lang['milestone_billing_status'] = 'Status';

// Additional missing strings
$lang['invoice_project_data_milestone_billing'] = 'Invoice based on milestones';
$lang['milestone_billing_invoice_item_name'] = 'Milestone name';
$lang['milestone_billing_invoice_item_description'] = 'Milestone description and billing details';
$lang['milestone_billing_select_milestones_for_invoice'] = 'Select the milestones you want to include in this invoice';
$lang['milestone_billing_select_all_completed'] = 'Select all completed milestones';
$lang['milestone_billing_selected_count'] = 'Selected Milestones';
$lang['milestone_billing_total_amount'] = 'Total Amount';
$lang['milestone_not_completed'] = 'Not completed';
$lang['all_projects'] = 'All Projects';
$lang['apply_filters'] = 'Apply Filters';
$lang['clear_filters'] = 'Clear Filters';
$lang['filters'] = 'Filters';
$lang['no_milestones_found'] = 'No milestones found';

// Billing Types
$lang['milestone_billing_fixed_rate'] = 'Fixed Rate';
$lang['milestone_billing_fixed_rate_desc'] = 'Bill a fixed amount when milestone is completed';
$lang['milestone_billing_percentage'] = 'Percentage of Project';
$lang['milestone_billing_percentage_desc'] = 'Bill a percentage of total project value';
$lang['milestone_billing_hourly'] = 'Hourly Rate';
$lang['milestone_billing_hourly_desc'] = 'Bill based on hours logged for milestone tasks';

// Form Labels
$lang['milestone_rate_label'] = 'Milestone Rate';
$lang['milestone_rate_help'] = 'Enter the rate for this milestone based on the billing type selected';
$lang['milestone_billing_type_label'] = 'Billing Type';
$lang['milestone_billing_type_help'] = 'Select how this milestone should be billed';
$lang['milestone_is_billable_label'] = 'Make this milestone billable';
$lang['milestone_is_billable_help'] = 'Check this to include this milestone in project billing';

// Invoice Related
$lang['milestone_billing_invoice_item'] = 'Milestone: %s';
$lang['milestone_billing_create_invoice'] = 'Create Invoice from Milestones';
$lang['milestone_billing_select_milestones'] = 'Select Milestones to Bill';
$lang['milestone_billing_no_milestones_selected'] = 'No milestones selected for billing';
$lang['milestone_billing_no_billable_amount'] = 'No billable amount found for selected milestones';
$lang['milestone_billing_invoice_created_successfully'] = 'Invoice created successfully from milestones';
$lang['milestone_billing_invoice_creation_failed'] = 'Failed to create invoice from milestones';

// Status Messages
$lang['milestone_billing_updated_successfully'] = 'Milestone billing information updated successfully';
$lang['milestone_billing_update_failed'] = 'Failed to update milestone billing information';
$lang['milestone_marked_completed'] = 'Milestone marked as completed';
$lang['milestone_completion_failed'] = 'Failed to mark milestone as completed';
$lang['milestone_billing_removed'] = 'Milestone billing removed successfully';
$lang['milestone_billing_removal_failed'] = 'Failed to remove milestone billing';

// Project Tab
$lang['milestone_billing_overview'] = 'Milestone Billing Overview';
$lang['milestone_billing_summary'] = 'Billing Summary';
$lang['milestone_billing_total_milestones'] = 'Total Milestones';
$lang['milestone_billing_billable_milestones'] = 'Billable Milestones';
$lang['milestone_billing_billed_milestones'] = 'Billed Milestones';
$lang['milestone_billing_completed_milestones'] = 'Completed Milestones';
$lang['milestone_billing_total_billable_amount'] = 'Total Billable Amount';
$lang['milestone_billing_total_billed_amount'] = 'Total Billed Amount';
$lang['milestone_billing_pending_amount'] = 'Pending Amount';

// Table Headers
$lang['milestone_billing_milestone_name'] = 'Milestone Name';
$lang['milestone_billing_due_date'] = 'Due Date';
$lang['milestone_billing_completion'] = 'Completion';
$lang['milestone_billing_logged_hours'] = 'Logged Hours';
$lang['milestone_billing_calculated_amount'] = 'Calculated Amount';
$lang['milestone_billing_actions'] = 'Actions';

// Actions
$lang['milestone_billing_mark_completed'] = 'Mark Completed';
$lang['milestone_billing_mark_billed'] = 'Mark Billed';
$lang['milestone_billing_unmark_billed'] = 'Unmark Billed';
$lang['milestone_billing_edit_billing'] = 'Edit Billing';
$lang['milestone_billing_view_invoice'] = 'View Invoice';

// Settings
$lang['milestone_billing_settings'] = 'Milestone Billing Settings';
$lang['milestone_billing_auto_bill'] = 'Auto-bill completed milestones';
$lang['milestone_billing_auto_bill_help'] = 'Automatically create invoices when milestones are completed';
$lang['milestone_billing_require_completion'] = 'Require milestone completion for billing';
$lang['milestone_billing_require_completion_help'] = 'Only allow billing of milestones that are 100% completed';
$lang['milestone_billing_default_type'] = 'Default Billing Type';
$lang['milestone_billing_default_rate'] = 'Default Rate';

// Validation Messages
$lang['milestone_billing_rate_required'] = 'Rate is required for billable milestones';
$lang['milestone_billing_type_required'] = 'Billing type is required for billable milestones';
$lang['milestone_billing_invalid_rate'] = 'Please enter a valid rate';
$lang['milestone_billing_invalid_percentage'] = 'Percentage must be between 0 and 100';

// Help Text
$lang['milestone_billing_help_fixed_rate'] = 'Enter a fixed amount to bill when this milestone is completed';
$lang['milestone_billing_help_percentage'] = 'Enter percentage (0-100) of total project value to bill';
$lang['milestone_billing_help_hourly'] = 'Enter hourly rate - will be multiplied by logged hours for milestone tasks';

// Activity Log
$lang['project_activity_milestone_invoiced'] = 'Milestone invoice created: %s';
$lang['project_activity_milestone_billed'] = 'Milestone marked as billed: %s';
$lang['project_activity_milestone_billing_updated'] = 'Milestone billing updated: %s';

// Statistics
$lang['milestone_billing_stats_title'] = 'Milestone Billing Statistics';
$lang['milestone_billing_stats_total_projects'] = 'Projects with Milestone Billing';
$lang['milestone_billing_stats_total_revenue'] = 'Total Milestone Revenue';
$lang['milestone_billing_stats_pending_revenue'] = 'Pending Milestone Revenue';
$lang['milestone_billing_stats_completion_rate'] = 'Milestone Completion Rate';

// Notifications
$lang['milestone_billing_notification_completed'] = 'Milestone "%s" has been completed and is ready for billing';
$lang['milestone_billing_notification_auto_billed'] = 'Milestone "%s" has been automatically billed';

// Errors
$lang['milestone_billing_error_no_project_cost'] = 'Project cost not set - cannot calculate percentage-based billing';
$lang['milestone_billing_error_no_logged_hours'] = 'No hours logged for milestone tasks - cannot calculate hourly billing';
$lang['milestone_billing_error_milestone_not_found'] = 'Milestone not found';
$lang['milestone_billing_error_already_billed'] = 'Milestone has already been billed';
$lang['milestone_billing_error_not_completed'] = 'Milestone is not completed yet';

// Modal Titles
$lang['milestone_billing_modal_create_invoice'] = 'Create Invoice from Milestones';
$lang['milestone_billing_modal_edit_billing'] = 'Edit Milestone Billing';
$lang['milestone_billing_modal_settings'] = 'Milestone Billing Settings';

// Buttons
$lang['milestone_billing_btn_create_invoice'] = 'Create Invoice';
$lang['milestone_billing_btn_save_settings'] = 'Save Settings';
$lang['milestone_billing_btn_update_billing'] = 'Update Billing';
$lang['milestone_billing_btn_mark_completed'] = 'Mark as Completed';
$lang['milestone_billing_btn_view_details'] = 'View Details';

// Tooltips
$lang['milestone_billing_tooltip_fixed'] = 'Fixed amount will be billed regardless of time spent';
$lang['milestone_billing_tooltip_percentage'] = 'Percentage of total project cost will be billed';
$lang['milestone_billing_tooltip_hourly'] = 'Rate per hour multiplied by logged time will be billed';
$lang['milestone_billing_tooltip_completed'] = 'All milestone tasks must be completed';
$lang['milestone_billing_tooltip_billed'] = 'This milestone has been included in an invoice';

// Progress Indicators
$lang['milestone_billing_progress_not_started'] = 'Not Started';
$lang['milestone_billing_progress_in_progress'] = 'In Progress';
$lang['milestone_billing_progress_completed'] = 'Completed';
$lang['milestone_billing_progress_billed'] = 'Billed';

// Export/Reports
$lang['milestone_billing_export_title'] = 'Milestone Billing Report';
$lang['milestone_billing_report_period'] = 'Report Period';
$lang['milestone_billing_report_project'] = 'Project';
$lang['milestone_billing_report_generate'] = 'Generate Report';
