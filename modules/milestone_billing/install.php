<?php

defined('BASEPATH') or exit('No direct script access allowed');

if (!$CI = &get_instance()) {
    return;
}

// Add rate and billing_type columns to milestones table
if (!$CI->db->field_exists('rate', db_prefix() . 'milestones')) {
    $CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD `rate` DECIMAL(15,2) DEFAULT NULL AFTER `color`');
}

if (!$CI->db->field_exists('billing_type', db_prefix() . 'milestones')) {
    $CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD `billing_type` TINYINT(1) DEFAULT NULL COMMENT "1=Fixed Rate, 2=Percentage, 3=Hourly" AFTER `rate`');
}

if (!$CI->db->field_exists('is_billable', db_prefix() . 'milestones')) {
    $CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD `is_billable` BOOLEAN DEFAULT FALSE AFTER `billing_type`');
}

if (!$CI->db->field_exists('billed', db_prefix() . 'milestones')) {
    $CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD `billed` BOOLEAN DEFAULT FALSE AFTER `is_billable`');
}

// Create milestone billing items table to track what has been billed
if (!$CI->db->table_exists(db_prefix() . 'milestone_billing_items')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'milestone_billing_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `milestone_id` int(11) NOT NULL,
        `invoice_id` int(11) NOT NULL,
        `project_id` int(11) NOT NULL,
        `amount` decimal(15,2) NOT NULL,
        `billing_type` tinyint(1) NOT NULL COMMENT "1=Fixed Rate, 2=Percentage, 3=Hourly",
        `hours_billed` decimal(8,2) DEFAULT NULL,
        `rate_used` decimal(15,2) DEFAULT NULL,
        `percentage_used` decimal(5,2) DEFAULT NULL,
        `description` text,
        `date_created` datetime NOT NULL,
        `created_by` int(11) NOT NULL,
        PRIMARY KEY (`id`),
        KEY `milestone_id` (`milestone_id`),
        KEY `invoice_id` (`invoice_id`),
        KEY `project_id` (`project_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1');
}

// Create milestone billing settings table
if (!$CI->db->table_exists(db_prefix() . 'milestone_billing_settings')) {
    $CI->db->query('CREATE TABLE `' . db_prefix() . 'milestone_billing_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `project_id` int(11) NOT NULL,
        `auto_bill_completed_milestones` boolean DEFAULT FALSE,
        `require_milestone_completion` boolean DEFAULT TRUE,
        `default_billing_type` tinyint(1) DEFAULT 1,
        `default_rate` decimal(15,2) DEFAULT NULL,
        `date_created` datetime NOT NULL,
        `date_updated` datetime NOT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `project_id` (`project_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1');
}

// Add indexes for better performance
$CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD INDEX `idx_billable` (`is_billable`)');
$CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD INDEX `idx_billed` (`billed`)');
$CI->db->query('ALTER TABLE `' . db_prefix() . 'milestones` ADD INDEX `idx_billing_type` (`billing_type`)');

// Insert default settings for existing projects with milestones
$CI->db->query('INSERT IGNORE INTO `' . db_prefix() . 'milestone_billing_settings` 
    (`project_id`, `auto_bill_completed_milestones`, `require_milestone_completion`, `default_billing_type`, `date_created`, `date_updated`)
    SELECT DISTINCT `project_id`, FALSE, TRUE, 1, NOW(), NOW()
    FROM `' . db_prefix() . 'milestones`
    WHERE `project_id` NOT IN (SELECT `project_id` FROM `' . db_prefix() . 'milestone_billing_settings`)');
