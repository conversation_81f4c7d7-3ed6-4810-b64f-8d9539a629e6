<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Milestone_billing_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get milestone with rate information
     */
    public function get_milestone_rate($milestone_id)
    {
        $this->db->select('id, name, description, rate, due_date');
        $this->db->where('id', $milestone_id);
        return $this->db->get(db_prefix() . 'milestones')->row_array();
    }

    /**
     * Get project milestones with rates
     */
    public function get_project_milestones_with_rates($project_id)
    {
        $this->db->select('id, name, description, rate, due_date');
        $this->db->where('project_id', $project_id);
        $this->db->where('rate >', 0);
        $this->db->order_by('milestone_order', 'ASC');
        return $this->db->get(db_prefix() . 'milestones')->result_array();
    }

    /**
     * Update milestone rate
     */
    public function update_milestone_rate($milestone_id, $rate)
    {
        $this->db->where('id', $milestone_id);
        return $this->db->update(db_prefix() . 'milestones', ['rate' => $rate]);
    }
}
