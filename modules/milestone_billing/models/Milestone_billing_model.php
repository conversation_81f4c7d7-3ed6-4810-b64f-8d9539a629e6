<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Milestone_billing_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get milestone with billing information
     */
    public function get_milestone_with_billing($milestone_id)
    {
        $this->db->select('m.*, p.project_cost, p.billing_type as project_billing_type');
        $this->db->from(db_prefix() . 'milestones m');
        $this->db->join(db_prefix() . 'projects p', 'p.id = m.project_id', 'left');
        $this->db->where('m.id', $milestone_id);
        
        return $this->db->get()->row_array();
    }

    /**
     * Get project milestones with billing information
     */
    public function get_project_milestones_with_billing($project_id)
    {
        $this->db->select('m.*, 
            (SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=' . $project_id . ' AND t.milestone=m.id) as total_tasks,
            (SELECT COUNT(t.id) FROM ' . db_prefix() . 'tasks t WHERE t.rel_type="project" AND t.rel_id=' . $project_id . ' AND t.milestone=m.id AND t.status=5) as completed_tasks,
            (SELECT SUM(tt.end_time - tt.start_time) FROM ' . db_prefix() . 'taskstimers tt 
             JOIN ' . db_prefix() . 'tasks t ON t.id = tt.task_id 
             WHERE t.milestone = m.id AND tt.end_time IS NOT NULL) as total_logged_seconds');
        $this->db->from(db_prefix() . 'milestones m');
        $this->db->where('m.project_id', $project_id);
        $this->db->order_by('m.milestone_order', 'ASC');
        
        $milestones = $this->db->get()->result_array();
        
        foreach ($milestones as &$milestone) {
            $milestone['total_logged_hours'] = $milestone['total_logged_seconds'] ? round($milestone['total_logged_seconds'] / 3600, 2) : 0;
            $milestone['completion_percentage'] = $milestone['total_tasks'] > 0 ? round(($milestone['completed_tasks'] / $milestone['total_tasks']) * 100, 2) : 0;
            $milestone['is_completed'] = $milestone['total_tasks'] > 0 && $milestone['completed_tasks'] == $milestone['total_tasks'];
            $milestone['calculated_amount'] = $this->calculate_milestone_amount($milestone, $project_id);
        }
        
        return $milestones;
    }

    /**
     * Calculate milestone billing amount based on billing type
     */
    public function calculate_milestone_amount($milestone, $project_id)
    {
        if (!$milestone['is_billable'] || !$milestone['rate'] || !$milestone['billing_type']) {
            return 0;
        }

        switch ($milestone['billing_type']) {
            case 1: // Fixed Rate
                return $milestone['rate'];
                
            case 2: // Percentage of Project
                $project = $this->get_project_cost($project_id);
                if ($project && $project['project_cost']) {
                    return ($milestone['rate'] / 100) * $project['project_cost'];
                }
                return 0;
                
            case 3: // Hourly Rate
                $hours = $this->get_milestone_logged_hours($milestone['id']);
                return $milestone['rate'] * $hours;
                
            default:
                return 0;
        }
    }

    /**
     * Get project cost information
     */
    public function get_project_cost($project_id)
    {
        $this->db->select('project_cost, billing_type');
        $this->db->where('id', $project_id);
        return $this->db->get(db_prefix() . 'projects')->row_array();
    }

    /**
     * Get total logged hours for milestone tasks
     */
    public function get_milestone_logged_hours($milestone_id)
    {
        $this->db->select('SUM(tt.end_time - tt.start_time) as total_seconds');
        $this->db->from(db_prefix() . 'taskstimers tt');
        $this->db->join(db_prefix() . 'tasks t', 't.id = tt.task_id');
        $this->db->where('t.milestone', $milestone_id);
        $this->db->where('tt.end_time IS NOT NULL');
        
        $result = $this->db->get()->row_array();
        return $result['total_seconds'] ? round($result['total_seconds'] / 3600, 2) : 0;
    }

    /**
     * Update milestone billing data
     */
    public function update_milestone_billing_data($milestone_id, $data)
    {
        $this->db->where('id', $milestone_id);
        return $this->db->update(db_prefix() . 'milestones', $data);
    }

    /**
     * Mark milestone as billed
     */
    public function mark_milestone_billed($milestone_id)
    {
        $this->db->where('id', $milestone_id);
        return $this->db->update(db_prefix() . 'milestones', ['billed' => 1]);
    }

    /**
     * Unmark milestone as billed
     */
    public function unmark_milestone_billed($milestone_id)
    {
        // First remove billing records
        $this->db->where('milestone_id', $milestone_id);
        $this->db->delete(db_prefix() . 'milestone_billing_items');
        
        // Then unmark milestone
        $this->db->where('id', $milestone_id);
        return $this->db->update(db_prefix() . 'milestones', ['billed' => 0]);
    }

    /**
     * Mark milestone as completed
     */
    public function mark_milestone_completed($milestone_id)
    {
        // This could trigger auto-billing if enabled
        $milestone = $this->get_milestone_with_billing($milestone_id);
        
        if ($milestone) {
            $settings = $this->get_project_billing_settings($milestone['project_id']);
            
            if ($settings && $settings['auto_bill_completed_milestones'] && $milestone['is_billable'] && !$milestone['billed']) {
                // Auto-create invoice for this milestone
                return $this->auto_bill_milestone($milestone_id);
            }
        }
        
        return true;
    }

    /**
     * Create billing record
     */
    public function create_billing_record($data)
    {
        $data['date_created'] = date('Y-m-d H:i:s');
        $this->db->insert(db_prefix() . 'milestone_billing_items', $data);
        return $this->db->insert_id();
    }

    /**
     * Get billable milestones for a project
     */
    public function get_billable_milestones($project_id)
    {
        $this->db->select('*');
        $this->db->from(db_prefix() . 'milestones');
        $this->db->where('project_id', $project_id);
        $this->db->where('is_billable', 1);
        $this->db->where('billed', 0);
        $this->db->order_by('milestone_order', 'ASC');
        
        $milestones = $this->db->get()->result_array();
        
        foreach ($milestones as &$milestone) {
            $milestone['calculated_amount'] = $this->calculate_milestone_amount($milestone, $project_id);
            $milestone['is_completed'] = $this->is_milestone_completed($milestone['id']);
        }
        
        return $milestones;
    }

    /**
     * Check if milestone is completed (all tasks finished)
     */
    public function is_milestone_completed($milestone_id)
    {
        $this->db->select('COUNT(*) as total_tasks');
        $this->db->from(db_prefix() . 'tasks');
        $this->db->where('milestone', $milestone_id);
        $total = $this->db->get()->row_array()['total_tasks'];
        
        if ($total == 0) {
            return false; // No tasks = not completed
        }
        
        $this->db->select('COUNT(*) as completed_tasks');
        $this->db->from(db_prefix() . 'tasks');
        $this->db->where('milestone', $milestone_id);
        $this->db->where('status', 5); // Status 5 = completed
        $completed = $this->db->get()->row_array()['completed_tasks'];
        
        return $completed == $total;
    }

    /**
     * Get project billing summary
     */
    public function get_project_billing_summary($project_id)
    {
        $summary = [
            'total_milestones' => 0,
            'billable_milestones' => 0,
            'billed_milestones' => 0,
            'completed_milestones' => 0,
            'total_billable_amount' => 0,
            'total_billed_amount' => 0,
            'pending_amount' => 0
        ];
        
        $milestones = $this->get_project_milestones_with_billing($project_id);
        
        foreach ($milestones as $milestone) {
            $summary['total_milestones']++;
            
            if ($milestone['is_billable']) {
                $summary['billable_milestones']++;
                $summary['total_billable_amount'] += $milestone['calculated_amount'];
                
                if ($milestone['billed']) {
                    $summary['billed_milestones']++;
                    $summary['total_billed_amount'] += $milestone['calculated_amount'];
                } else {
                    $summary['pending_amount'] += $milestone['calculated_amount'];
                }
            }
            
            if ($milestone['is_completed']) {
                $summary['completed_milestones']++;
            }
        }
        
        return $summary;
    }

    /**
     * Get milestone billing statistics
     */
    public function get_milestone_billing_statistics()
    {
        // Global statistics across all projects
        $stats = [];
        
        // Total billable milestones
        $this->db->select('COUNT(*) as count');
        $this->db->where('is_billable', 1);
        $stats['total_billable'] = $this->db->get(db_prefix() . 'milestones')->row_array()['count'];
        
        // Total billed milestones
        $this->db->select('COUNT(*) as count');
        $this->db->where('is_billable', 1);
        $this->db->where('billed', 1);
        $stats['total_billed'] = $this->db->get(db_prefix() . 'milestones')->row_array()['count'];
        
        // Total billed amount
        $this->db->select('SUM(amount) as total');
        $stats['total_amount'] = $this->db->get(db_prefix() . 'milestone_billing_items')->row_array()['total'] ?: 0;
        
        return $stats;
    }

    /**
     * Get project milestone billing statistics
     */
    public function get_project_milestone_billing_statistics($project_id)
    {
        return $this->get_project_billing_summary($project_id);
    }

    /**
     * Get project billing settings
     */
    public function get_project_billing_settings($project_id)
    {
        $this->db->where('project_id', $project_id);
        return $this->db->get(db_prefix() . 'milestone_billing_settings')->row_array();
    }

    /**
     * Update project billing settings
     */
    public function update_project_billing_settings($project_id, $data)
    {
        $data['date_updated'] = date('Y-m-d H:i:s');

        $this->db->where('project_id', $project_id);
        $existing = $this->db->get(db_prefix() . 'milestone_billing_settings')->row_array();

        if ($existing) {
            $this->db->where('project_id', $project_id);
            return $this->db->update(db_prefix() . 'milestone_billing_settings', $data);
        } else {
            $data['project_id'] = $project_id;
            $data['date_created'] = date('Y-m-d H:i:s');
            $this->db->insert(db_prefix() . 'milestone_billing_settings', $data);
            return $this->db->insert_id();
        }
    }

    /**
     * Auto-bill milestone when completed
     */
    private function auto_bill_milestone($milestone_id)
    {
        $milestone = $this->get_milestone_with_billing($milestone_id);

        if (!$milestone || $milestone['billed'] || !$milestone['is_billable']) {
            return false;
        }

        // Create invoice data
        $invoice_data = [
            'clientid' => $this->get_project_client_id($milestone['project_id']),
            'project_id' => $milestone['project_id'],
            'date' => date('Y-m-d'),
            'duedate' => date('Y-m-d', strtotime('+30 days')),
            'currency' => get_base_currency()->id,
            'newitems' => []
        ];

        $amount = $this->calculate_milestone_amount($milestone, $milestone['project_id']);

        if ($amount > 0) {
            $invoice_data['newitems'][] = [
                'description' => _l('milestone_billing_invoice_item', $milestone['name']),
                'long_description' => $milestone['description'],
                'qty' => 1,
                'rate' => $amount,
                'taxname_1' => '',
                'taxrate_1' => 0,
                'taxname_2' => '',
                'taxrate_2' => 0,
                'unit' => ''
            ];

            // Load invoices model and create invoice
            $CI = &get_instance();
            $CI->load->model('invoices_model');

            $invoice_id = $CI->invoices_model->add($invoice_data);

            if ($invoice_id) {
                // Mark milestone as billed
                $this->mark_milestone_billed($milestone_id);

                // Create billing record
                $this->create_billing_record([
                    'milestone_id' => $milestone_id,
                    'invoice_id' => $invoice_id,
                    'project_id' => $milestone['project_id'],
                    'amount' => $amount,
                    'billing_type' => $milestone['billing_type'],
                    'rate_used' => $milestone['rate'],
                    'description' => _l('milestone_billing_invoice_item', $milestone['name']),
                    'created_by' => get_staff_user_id()
                ]);

                return $invoice_id;
            }
        }

        return false;
    }

    /**
     * Get project client ID
     */
    private function get_project_client_id($project_id)
    {
        $this->db->select('clientid');
        $this->db->where('id', $project_id);
        $result = $this->db->get(db_prefix() . 'projects')->row_array();

        return $result ? $result['clientid'] : null;
    }
}
